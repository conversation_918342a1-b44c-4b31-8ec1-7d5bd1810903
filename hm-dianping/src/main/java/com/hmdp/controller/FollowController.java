package com.hmdp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import com.hmdp.dto.Result;
import com.hmdp.service.IFollowService;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@RestController
@RequestMapping("/follow")
public class FollowController {
    @Autowired
    private IFollowService followService;

    // TODO实现关注接口
    // 关注表+redis中的set功能,注意先表后缓存

    /**
     * 修改关注状态
     * 
     * @param userIdToFollow
     * @param isFollow
     * @return
     */
    @PutMapping("/{id}/{isFollow}")
    public Result follow(
            @PathVariable("id") Long userIdToFollow,
            @PathVariable("isFollow") Boolean isFollow) {
        return followService.follow(userIdToFollow, isFollow);
    }

    /**
     * 查询是否关注某用户
     * 
     * @param FollowUserId
     * @return
     */
    @GetMapping("/or/not/{id}")
    public Result isFollow(@PathVariable("id") Long FollowUserId) {
        return followService.isFollow(FollowUserId);
    }

    @GetMapping("/common/{id}")
    public Result followCommon(@PathVariable("id") Long id) {
        return followService.followCommon(id);
    }
}
