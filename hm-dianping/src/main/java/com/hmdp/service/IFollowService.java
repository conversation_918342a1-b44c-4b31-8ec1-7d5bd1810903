package com.hmdp.service;

import com.hmdp.dto.Result;
import com.hmdp.entity.Follow;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
public interface IFollowService extends IService<Follow> {

    /**
     * 关注/取关用户
     * 
     * @param userIdToFollow
     * @param isFollow
     * @return
     */
    Result follow(Long userIdToFollow, Boolean isFollow);

    /**
     * 查询是否关注
     * 
     * @param followUserId
     * @return
     */
    Result isFollow(Long followUserId);

    /**
     * 查询共同关注
     * 
     * @param id
     * @return
     */
    Result followCommon(Long id);

}
