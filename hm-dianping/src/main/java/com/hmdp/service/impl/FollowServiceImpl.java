package com.hmdp.service.impl;

import static com.hmdp.utils.RedisConstants.FOLLOW_KEY;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.Result;
import com.hmdp.entity.Follow;
import com.hmdp.entity.User;
import com.hmdp.mapper.FollowMapper;
import com.hmdp.service.IFollowService;
import com.hmdp.service.IUserService;
import com.hmdp.utils.UserHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
public class FollowServiceImpl extends ServiceImpl<FollowMapper, Follow> implements IFollowService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IUserService userService;

    @Override
    public Result follow(Long userIdToFollow, Boolean isFollow) {
        // 1.检查用户是否关注
        // 1.1获取当前用户id
        Long userId = UserHolder.getUser().getId();
        // 1.2获取是否关注博主 [follow:userId ==> userIdToFollow]
        String key = FOLLOW_KEY + userId;
        Boolean isMember = stringRedisTemplate.opsForSet().isMember(key, userIdToFollow.toString());

        if (BooleanUtil.isFalse(isMember)) {
            // 3.用户未关注,实现当前用户关注待关注用户
            if (BooleanUtil.isTrue(isFollow)) {
                // 需要关注用户

                // 4.1关注关系表入表
                Follow follow = new Follow();
                follow.setFollowUserId(userIdToFollow);
                follow.setUserId(userId);
                boolean isSave = save(follow);

                // 4.2redis缓存添加
                if (isSave) {
                    stringRedisTemplate.opsForSet().add(key, userIdToFollow.toString());
                }

            }
        } else {
            // 3.2用户已关注,取关
            if (BooleanUtil.isFalse(isFollow)) {
                // 需要取关用户

                // 4.1删除数据库表
                boolean isRemove = remove(
                        new LambdaQueryWrapper<Follow>()
                                .eq(Follow::getUserId, userId)
                                .eq(Follow::getFollowUserId, userIdToFollow));
                if (isRemove) {
                    // 4.2redis缓存删除
                    stringRedisTemplate.opsForSet().remove(key, userIdToFollow.toString());
                }

            }
        }

        return Result.ok();
    }

    @Override
    public Result isFollow(Long followUserId) {
        // 1. 查询redis是否关注
        // 1.1获取当前用户id
        Long userId = UserHolder.getUser().getId();
        // 1.2获取是否关注博主 [follow:userId ==> userIdToFollow]
        String key = FOLLOW_KEY + userId;
        Boolean isMember = stringRedisTemplate.opsForSet().isMember(key, followUserId.toString());

        // 2.返回数据
        return Result.ok(BooleanUtil.isTrue(isMember));
    }

    /**
     * 查询共同关注
     */
    @Override
    public Result followCommon(Long id) {
        // 1.查询当前登录用户id
        Long userId = UserHolder.getUser().getId();

        // 2.查询交集同关注
        // 2.1获取key
        String userIdKey = FOLLOW_KEY + userId;
        String idKey = FOLLOW_KEY + id;

        // 查询交集
        Set<String> interSet = stringRedisTemplate.opsForSet().intersect(userIdKey, idKey);
        if (CollUtil.isEmpty(interSet)) {
            // 没有共同关注
            return Result.ok(Collections.emptyList());
        }

        if (interSet == null) {
            return Result.fail("服务器错误");
        }
        // 存在交集
        // 获取userId然后查询相关的信息并封装
        List<Long> commonUserIds = interSet.stream().map(Long::valueOf).collect(Collectors.toList());
        // 查询相关数据库
        List<User> commonUsers = userService.listByIds(commonUserIds);
        return Result.ok(commonUsers);
    }

}
