package com.hmdp.service.impl;

import static com.hmdp.utils.RedisConstants.CACHE_SHOP_KEY;
import static com.hmdp.utils.RedisConstants.CACHE_SHOP_TTL;
import static com.hmdp.utils.RedisConstants.LOCK_SHOP_KEY;
import static com.hmdp.utils.RedisConstants.SHOP_GEO_KEY;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.GeoResult;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.connection.RedisGeoCommands.GeoLocation;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.domain.geo.GeoReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.Result;
import com.hmdp.entity.Shop;
import com.hmdp.mapper.ShopMapper;
import com.hmdp.service.IShopService;
import com.hmdp.utils.CacheClient;
import com.hmdp.utils.SystemConstants;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Slf4j
@Service
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements IShopService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private CacheClient cacheClient;

    /**
     * 缓存重建线程池
     */
    public static final ExecutorService CACHE_REBUILD_EXECUTOR = Executors.newFixedThreadPool(10);

    /**
     * 根据id获取店铺详细信息
     */
    @Override
    public Result queryShopById(Long id) {
        // //0.普通查询数据库
        // Shop shop = getById(id);

        // 1.使用普通缓存
        // return queryWithCache(id);

        // 2.解决缓存穿透
        // return queryWithEmptyCache(id);

        // 3.解决缓存击穿
        // return queryWithMutex(id); // 使用分布式锁
        // return queryWithLogicalExpireTime(id); // 使用逻辑过期
        Shop shop = cacheClient.queryWithLogicalExpireTime(CACHE_SHOP_KEY, LOCK_SHOP_KEY, id, Shop.class, this::getById,
                CACHE_SHOP_TTL, TimeUnit.MINUTES);

        if (shop == null) {
            return Result.fail("店铺不存在");
        }

        return Result.ok(shop);

    }

    /**
     * 更新店铺
     */
    @Transactional
    @Override
    public Result updateShop(Shop shop) {
        // 1.更新数据库
        boolean isUpdate = updateById(shop);
        if (!isUpdate) {
            // 更新失败
            log.error("更新数据库失败:{}", shop);
            throw new RuntimeException("更新数据库失败");
        }

        // 2.删除缓存
        String shopKey = CACHE_SHOP_KEY + shop.getId();
        boolean isDelete = stringRedisTemplate.delete(shopKey);
        log.warn("正在删除店铺key:{}", shop.getId());
        if (!isDelete) {
            // 删除失败
            log.error("缓存删除失败:{}", shop);
            throw new RuntimeException("缓存删除失败");
        }

        // 3.返回结果
        return Result.ok();
    }

    /**
     * 类型检索店铺
     */
    @Override
    public Result queryShopByType(Integer typeId, Integer current, Double x, Double y) {
        if (x == null || y == null) {
            // 无需根据位置查询
            // 不需要坐标查询，按数据库查询，根据类型分页查询
            Page<Shop> page = query().eq("type_id", typeId)
                    .page(new Page<>(current, SystemConstants.DEFAULT_PAGE_SIZE));
            // 返回数据
            return Result.ok(page.getRecords());
        }

        // 需要根据位置查询
        int from = (current - 1) * DEFAULT_BATCH_SIZE; // 起始数据
        int end = current * DEFAULT_BATCH_SIZE; // 结束数据

        // 查询redis
        // 获取key
        String key = SHOP_GEO_KEY + typeId;
        // 查询
        GeoResults<RedisGeoCommands.GeoLocation<String>> results = stringRedisTemplate.opsForGeo().search(
                key,
                GeoReference.fromCoordinate(x, y), // 查询以给定的经纬度为中心的圆形区域
                new Distance(10000), // 查询10km范围内的店铺，单位默认为米
                RedisGeoCommands.GeoSearchCommandArgs.newGeoSearchArgs().includeDistance().limit(end) // 分页查询0~end条
        );
        if (results == null) {
            return Result.ok(Collections.emptyList());
        }

        // 解析数据
        List<GeoResult<GeoLocation<String>>> list = results.getContent();
        if (list.size() <= from) {
            // 说明需要查询的from - end部分数据为空
            return Result.ok(Collections.emptyList());
        }

        // 解析真实数据并封装
        List<Long> ids = new ArrayList<>(list.size());
        Map<String, Distance> distanceMap = new HashMap<>(list.size());
        list.stream().skip(from).forEach(res -> {
            // 添加ids
            String shopIdStr = res.getContent().getName();
            ids.add(Long.valueOf(shopIdStr));

            // 添加店铺距离
            Distance distance = res.getDistance();
            distanceMap.put(shopIdStr, distance);
        });

        // 根据id查询店铺数据
        String idStr = StrUtil.join(",", ids);
        List<Shop> shops = query().in("id", ids).last("ORDER BY FIELD(id," + idStr + ")").list();
        for (Shop shop : shops) {
            shop.setDistance(distanceMap.get(shop.getId().toString()).getValue());
        }

        return Result.ok(shops);
    }

    // /**
    // * 使用逻辑过期解决缓存击穿
    // * 默认需要使用该方法的数据已经提前预热
    // *
    // * @param id
    // * @return
    // */
    // private Result queryWithLogicalExpireTime(Long id) {
    // // 1.查询缓存
    // String key = CACHE_SHOP_KEY + id;
    // String redisDataJson = stringRedisTemplate.opsForValue().get(key);
    // if (StrUtil.isBlank(redisDataJson)) {
    // // 缓存为空,数据不存在直接返回
    // log.info("店铺不存在");
    // return Result.fail("店铺不存在");
    // }
    // // 1.2缓存不为空,将json序列化为对象,并判断是否过期
    // RedisData redisData = JSONUtil.toBean(redisDataJson, RedisData.class);
    // Shop shop = JSONUtil.toBean((JSONObject) redisData.getData(), Shop.class); //
    // 店铺实体数据
    // LocalDateTime expireDateTime = redisData.getExpireTime(); // 逻辑过期时间

    // // 2.检查缓存是否过期
    // if (LocalDateTime.now().isBefore(expireDateTime)) {
    // // 2.1缓存未过期返回数据
    // return Result.ok(shop);
    // }
    // log.info("数据已过期成功");
    // // 2.2缓存已过期,尝试获取锁,开启新线程重建缓存并返回过期信息避免阻塞
    // String lockKey = LOCK_SHOP_KEY + id;
    // boolean isLock = tryLock(lockKey);
    // if (isLock) {
    // // 3.1获取锁成功,重建缓存并释放锁
    // log.info("获取锁成功");
    // // 重建缓存
    // CACHE_REBUILD_EXECUTOR.submit(() -> {
    // try {
    // this.saveShopToCacheWithLogicalExpireTime(id, CACHE_SHOP_TTL);
    // } finally {
    // unLock(lockKey);
    // log.info("释放锁成功");
    // }
    // });
    // }

    // // 3.2获取锁失败检查信息再次获取缓存,检查信息是否过期
    // // 因为过期的信息是错误的,如果没有获取锁那就是其他线程正在重建缓存,那么此时返回过期信息没问题
    // // 如果未获取锁重建的时候二次检查可以避免其他线程已经重建完成了,但是重建瞬间涌入的线程还是返回错误信息
    // // 1.查询缓存
    // redisDataJson = stringRedisTemplate.opsForValue().get(key);
    // if (StrUtil.isBlank(redisDataJson)) {
    // // 缓存为空,数据不存在直接返回
    // return Result.fail("店铺不存在");
    // }
    // // 1.2缓存不为空,将json序列化为对象,并判断是否过期
    // redisData = JSONUtil.toBean(redisDataJson, RedisData.class);
    // shop = JSONUtil.toBean((JSONObject) redisData.getData(), Shop.class); //
    // 店铺实体数据
    // expireDateTime = redisData.getExpireTime(); // 逻辑过期时间

    // // 2.检查缓存是否过期
    // if (LocalDateTime.now().isBefore(expireDateTime)) {
    // // 2.1缓存未过期返回数据
    // return Result.ok(shop);
    // }

    // // 返回过期信息
    // return Result.ok(shop);

    // }

    // /**
    // * 查询数据库,封装未RedisData添加缓存
    // *
    // * @param id
    // * @param cacheShopTtl
    // */
    // private void saveShopToCacheWithLogicalExpireTime(Long id, Long cacheShopTtl)
    // {
    // // 1.查询店铺shop数据并封装
    // Shop shop = getById(id);
    // RedisData redisData = new RedisData();
    // redisData.setData(shop);
    // redisData.setExpireTime(LocalDateTime.now().plusMinutes(cacheShopTtl));

    // // 2.添加缓存
    // String key = CACHE_SHOP_KEY + id;
    // stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(redisData));
    // }

    // /**
    // * 缓存空数据解决缓存穿透
    // *
    // * @param id
    // * @return
    // */
    // private Result queryWithEmptyCache(Long id) {
    // // 从缓存获取那里抄来的大部分

    // // 1. 查询缓存
    // String shopKey = CACHE_SHOP_KEY + id;
    // Result result = getShopFromCacheByShopKey(shopKey);

    // if (result != null) {
    // // 存在真实数据or空数据
    // return result;
    // }

    // // 2.3缓存没有数据,查询数据库
    // Shop shop = getById(id);

    // if (shop == null) {
    // // 3.1数据库信息不存在,缓存空数据""
    // stringRedisTemplate.opsForValue().set(shopKey, "");
    // stringRedisTemplate.expire(shopKey, CACHE_NULL_TTL, TimeUnit.MINUTES); //
    // 空数据的缓存时间不同

    // // 4.返回数据
    // return Result.fail("店铺不存在");
    // } else {
    // // 3.2数据库信息存在,真实数据写入缓存
    // stringRedisTemplate.opsForValue().set(shopKey, JSONUtil.toJsonStr(shop));
    // stringRedisTemplate.expire(shopKey, CACHE_SHOP_TTL, TimeUnit.MINUTES);

    // // 4.返回数据
    // return Result.ok(shop);
    // }

    // }

    // /**
    // * 分布式锁解决缓存击穿的查询
    // *
    // * @param id
    // * @return
    // */
    // private Result queryWithMutex(Long id) {
    // // 1.查询缓存
    // String shopKey = CACHE_SHOP_KEY + id;
    // Result result = getShopFromCacheByShopKey(shopKey);
    // if (result != null) {
    // // 存在真实数据or空数据
    // return result;
    // }

    // // 来到这里缓存肯定不存在了,因此在这里组装锁key
    // String shopLockKey = LOCK_SHOP_KEY + id;
    // log.warn("找不到缓存");
    // try {
    // // 缓存不存在,尝试获取锁
    // boolean isGetLock = tryLock(shopLockKey);

    // // 3.检查锁
    // if (!isGetLock) {
    // // 未获取锁,休眠并重试
    // log.warn("没有拿到锁家人们,我先睡觉了");
    // Thread.sleep(50L);
    // return queryWithMutex(id); // 使用递归作为再次尝试的方法
    // }

    // // 已经获取锁,二次查询是否重建缓存
    // Result resultForDoubleCheck = getShopFromCacheByShopKey(shopKey);
    // if (resultForDoubleCheck != null) {
    // // 存在真实数据or空数据
    // return resultForDoubleCheck;
    // }

    // // 4.获取锁后缓存仍然过期,查询数据库
    // Shop shop = getById(id);
    // log.warn("开始重建缓存");
    // // 5.重建缓存
    // if (shop == null) {
    // // 数据库信息不存在,缓存空数据""
    // stringRedisTemplate.opsForValue().set(shopKey, "");// 缓存空数据避免缓存穿透
    // stringRedisTemplate.expire(shopKey, CACHE_NULL_TTL, TimeUnit.MINUTES);

    // return Result.fail("店铺不存在");

    // } else {
    // // 数据库信息存在,真实数据写入缓存
    // stringRedisTemplate.opsForValue().set(shopKey, JSONUtil.toJsonStr(shop));
    // stringRedisTemplate.expire(shopKey, CACHE_SHOP_TTL, TimeUnit.MINUTES);

    // return Result.ok(shop);
    // }

    // } catch (Exception e) {
    // e.printStackTrace();
    // return Result.fail("系统繁忙");
    // } finally {
    // // 释放锁

    // unLock(shopLockKey);
    // }

    // }

    // /**
    // * 释放分布式锁
    // *
    // * @param shopKey
    // */
    // private void unLock(String shopLockKey) {
    // stringRedisTemplate.delete(shopLockKey);
    // }

    // /**
    // * 获取分布式锁
    // *
    // * @param shopKey
    // * @return
    // */
    // private boolean tryLock(String shopLockKey) {
    // Boolean isLock = stringRedisTemplate.opsForValue().setIfAbsent(
    // shopLockKey,
    // "1",
    // LOCK_SHOP_TTL,
    // TimeUnit.SECONDS);
    // return BooleanUtil.isTrue(isLock);
    // }

    // private Result getShopFromCacheByShopKey(String shopKey) {
    // // 1. 查询缓存
    // String shopString = stringRedisTemplate.opsForValue().get(shopKey);

    // if (StrUtil.isNotBlank(shopString)) {
    // // 2.1缓存存在数据直接返回
    // return Result.ok(JSONUtil.toBean(shopString, Shop.class));
    // }

    // if (ObjectUtil.isNotNull(shopString)) {
    // // 2.2 缓存存在但是数据为空,返回空数据
    // return Result.fail("店铺不存在");
    // }

    // // 连空数据也没有
    // return null;

    // }

    // /**
    // * 简单使用redis缓存完成店铺信息获取
    // *
    // * @param id
    // * @return
    // */
    // private Result queryWithCache(Long id) {
    // // 1. 查询缓存
    // String shopKey = CACHE_SHOP_KEY + id;
    // String shopString = stringRedisTemplate.opsForValue().get(shopKey);

    // if (StrUtil.isNotBlank(shopString)) {
    // // 2.1缓存存在直接返回
    // return Result.ok(JSONUtil.toBean(shopString, Shop.class));
    // }
    // // 2.2缓存不存在查询数据库
    // Shop shop = getById(id);

    // if (shop == null) {
    // // 3.1数据库信息不存在,返回null
    // return Result.fail("店铺不存在");
    // }
    // // 3.2数据库信息存在,写入缓存
    // stringRedisTemplate.opsForValue().set(shopKey, JSONUtil.toJsonStr(shop));
    // stringRedisTemplate.expire(shopKey, CACHE_SHOP_TTL, TimeUnit.MINUTES);

    // // 4.返回数据
    // return Result.ok(shop);
    // }

}
