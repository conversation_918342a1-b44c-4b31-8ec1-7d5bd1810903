package com.hmdp.service.impl;

import static com.hmdp.utils.RedisConstants.LOGIN_CODE_KEY;
import static com.hmdp.utils.RedisConstants.LOGIN_CODE_TTL;
import static com.hmdp.utils.RedisConstants.LOGIN_USER_KEY;
import static com.hmdp.utils.RedisConstants.LOGIN_USER_TTL;
import static com.hmdp.utils.RedisConstants.USER_SIGN_KEY;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.connection.BitFieldSubCommands.BitFieldType;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.LoginFormDTO;
import com.hmdp.dto.Result;
import com.hmdp.dto.UserDTO;
import com.hmdp.entity.User;
import com.hmdp.mapper.UserMapper;
import com.hmdp.service.IUserService;
import com.hmdp.utils.MapConvertor;
import com.hmdp.utils.RegexUtils;
import com.hmdp.utils.SystemConstants;
import com.hmdp.utils.UserHolder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 发送验证码
     */
    @Override
    public Result sendCode(String phone) {
        // 1.验证手机号码合法性
        if (RegexUtils.isPhoneInvalid(phone)) {
            // 1.1号码不合法
            return Result.fail("号码不合法");
        }

        // 1.2号码合法
        // 2.生成随机验证码
        String code = RandomUtil.randomNumbers(6);

        // 3.验证码存放在redis中
        String codeKey = LOGIN_CODE_KEY + phone;
        stringRedisTemplate.opsForValue().set(codeKey, code, LOGIN_CODE_TTL, TimeUnit.MINUTES);

        // 4.返回信息
        return Result.ok(code);
    }

    /**
     * 用户登陆
     */
    @Override
    public Result login(LoginFormDTO loginForm) {
        // 1.检验手机号码
        String phone = loginForm.getPhone();
        if (RegexUtils.isPhoneInvalid(phone)) {
            // 手机号码不合法
            return Result.fail("号码不合法");
        }

        // 2.检验验证码
        String code = stringRedisTemplate.opsForValue().get(LOGIN_CODE_KEY + phone);
        if (code == null) {
            // 拿不到验证码
            return Result.fail("验证码已过期");
        }

        if (!code.equals(loginForm.getCode())) {
            // 验证码不正确
            return Result.fail("验证码不正确");
        }

        // 3.通过手机号查询数据库用户
        User user = this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getPhone, phone));

        if (user == null) {
            // 用户不存在完成用户注册并新增用户
            user = createWithPhone(phone); // 封装user用于后面复制
        }

        // 4.封装用户主要信息
        UserDTO userDTO = BeanUtil.copyProperties(user, UserDTO.class);

        // 5.生成随机token
        String token = UUID.randomUUID().toString();

        // 6.redis缓存用户token::userDTO
        // 6.1 选择使用hash数值类型,因此封装map
        Map<String, String> userMap = MapConvertor.convert(userDTO);
        // 6.2redis缓存数据,使用hash类型
        String tokenKey = LOGIN_USER_KEY + token;
        stringRedisTemplate.opsForHash().putAll(tokenKey, userMap); // 放置用户登录信息
        stringRedisTemplate.expire(tokenKey, LOGIN_USER_TTL, TimeUnit.SECONDS);

        // 7.返回token
        return Result.ok(token);
    }

    /**
     * 使用电话号码创建新用户
     * 
     * @param phone
     * @return
     */
    private User createWithPhone(String phone) {
        User newUser = new User();
        newUser.setPhone(phone); // 设置手机号码
        newUser.setNickName(SystemConstants.USER_NICK_NAME_PREFIX + RandomUtil.randomString(10)); // 设置昵称
        this.save(newUser); // 新用户插入数据表
        return newUser;
    }

    @Override
    public Result sign() {
        // 获取当前用户
        Long userId = UserHolder.getUser().getId();

        // 获取当前日期
        LocalDate now = LocalDate.now();
        String date = now.format(DateTimeFormatter.ofPattern("yyyy:MM"));

        // 获取key
        String key = USER_SIGN_KEY + date + userId;

        // redis签到
        // 获取今日得到offset
        int dayOfMonth = now.getDayOfMonth();
        int offset = dayOfMonth - 1;
        // 签到
        stringRedisTemplate.opsForValue().setBit(key, offset, true);

        return Result.ok();
    }

    @Override
    public Result signCount() {
        // 获取当前用户
        Long userId = UserHolder.getUser().getId();

        // 获取此时处在月份的bitmap的值
        LocalDate now = LocalDate.now();
        String date = now.format(DateTimeFormatter.ofPattern("yyyy:MM"));
        // 获取key
        String key = USER_SIGN_KEY + date + userId;

        // redis签到
        // 获取今日得到offset
        int totalBit = now.getDayOfMonth();
        BitFieldSubCommands subCommands = BitFieldSubCommands.create().get(BitFieldType.unsigned(totalBit)).valueAt(0);
        List<Long> bitField = stringRedisTemplate.opsForValue().bitField(key, subCommands);

        // 检查结果
        if (CollUtil.isEmpty(bitField)) {
            return Result.ok(0);
        }

        if (bitField == null) {
            return Result.ok(0);
        }

        // 遍历bitmap的值,每次&1并移位获取从现在开始连续的1有多少
        long num = bitField.get(0);
        int count = 0;
        while ((num & 1) != 0) {
            // 计数
            count++;
            // 移位
            num >>= 1;
        }
        return Result.ok(count);
    }

}
