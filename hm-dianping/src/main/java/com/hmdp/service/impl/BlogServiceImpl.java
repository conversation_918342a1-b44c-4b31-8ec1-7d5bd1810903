package com.hmdp.service.impl;

import static com.hmdp.utils.RedisConstants.BLOG_LIKED_KEY;
import static com.hmdp.utils.RedisConstants.FEED_KEY;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
// import com.sun.org.apache.regexp.internal.RE;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.Result;
import com.hmdp.dto.ScrollResult;
import com.hmdp.dto.UserDTO;
import com.hmdp.entity.Blog;
import com.hmdp.entity.Follow;
import com.hmdp.entity.User;
import com.hmdp.mapper.BlogMapper;
import com.hmdp.service.IBlogService;
import com.hmdp.service.IFollowService;
import com.hmdp.service.IUserService;
import com.hmdp.utils.UserHolder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.experimental.var;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
@Slf4j
public class BlogServiceImpl extends ServiceImpl<BlogMapper, Blog> implements IBlogService {

    @Resource
    private IFollowService followService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IUserService userService;

    @Override
    public Result saveBlog(Blog blog) {

        // 获取登录用户
        UserDTO user = UserHolder.getUser();
        blog.setUserId(user.getId());
        // 保存探店博文
        boolean isSave = save(blog);
        if (!isSave)
            return Result.fail("笔记保存失败");
        // 查询作者的所有粉丝
        List<Follow> followList = followService.query().eq("follow_user_id", blog.getUserId()).list();
        // 推送id给所有粉丝
        for (Follow follow : followList) {
            // 获取粉丝id
            Long userId = follow.getUserId();
            String key = FEED_KEY + userId;
            // 添加blogid到粉丝收件箱，zset
            stringRedisTemplate.opsForZSet().add(key, blog.getId().toString(), System.currentTimeMillis());
        }

        // 返回id
        return Result.ok(blog.getId());
    }

    @Override
    public Result queryBlogById(Long id) {
        // 查询blog
        Blog blog = getById(id);
        // 检查blog是否存在
        if (blog == null) {
            return Result.fail("blog不存在");
        }
        // 查询blog发布人信息
        queryBlogUser(blog);
        // TODO查询是否被点赞
        queryBlogIsLiked(blog);
        return Result.ok(blog);
    }

    private void queryBlogIsLiked(Blog blog) {
        // 获取用户id
        Long userId = UserHolder.getUser().getId();
        // 查询redis是否已经点赞
        String key = BLOG_LIKED_KEY + blog.getId();

        // 使用ZSET实现点赞功能
        Double score = stringRedisTemplate.opsForZSet().score(key, userId.toString());
        if (score == null) {
            // 未点赞
            blog.setIsLike(false);
        } else {
            // 已点赞
            blog.setIsLike(true);
        }

    }

    /**
     * 为传入的blog添加发布人信息
     * 
     * @param blog
     * @throws Exception
     */
    private void queryBlogUser(Blog blog) {
        // 获取发布人id
        Long userId = blog.getUserId();

        // 查询发布人信息
        User user = userService.getById(userId);

        // 封装发布人信息
        // 1.1发布人名称
        blog.setName(user.getNickName());
        // 1.2发布人图标
        blog.setIcon(user.getIcon());
    }

    @Override
    public Result likeBlog(Long id) {
        // 获取用户id
        Long userId = UserHolder.getUser().getId();
        // // 1.检查set中是否有当前用户的点赞
        // String key = BLOG_LIKED_KEY + id;
        // Boolean isLikedBoolean = stringRedisTemplate.opsForSet().isMember(key,
        // userId.toString());
        // 1.检查zset中是否有当前用户的点赞
        String key = BLOG_LIKED_KEY + id;
        Double score = stringRedisTemplate.opsForZSet().score(key, userId.toString());

        if (score == null) {
            // 2.1用户未点赞则点赞+1

            // 3.1数据库修改
            boolean isUpdate = update().setSql("liked = liked + 1").eq("id", id).update();
            // 3.2redis中分数加1
            if (isUpdate) {
                stringRedisTemplate.opsForZSet().add(key, userId.toString(), System.currentTimeMillis());
            }
        } else {
            // 2.2用户点赞了则取消点赞,点赞-1
            // 3.1数据库修改
            boolean isUpdate = update().setSql("liked = liked - 1").eq("id", id).update();
            // 3.2redis添加
            if (isUpdate) {
                stringRedisTemplate.opsForZSet().remove(key, userId.toString());
            }
        }

        return Result.ok();

    }

    /**
     * 获取点赞前五名
     */
    @Override
    public Result queryBlogLikes(Long id) {
        // 获取key
        String key = BLOG_LIKED_KEY + id;
        // 获取点赞前五名的userId
        Set<String> top5 = stringRedisTemplate.opsForZSet().range(key, 0, 4); // 获取时间戳前5名
        // 根据点赞前五名查询数据库并返回信息
        if (CollUtil.isEmpty(top5) || top5 == null) {
            return Result.ok(Collections.emptyList());
        }

        // 解析出用户id
        List<Long> userIds = top5.stream().map(Long::valueOf).collect(Collectors.toList());
        // 查询数据库获取用户信息
        String userIdsStr = StrUtil.join(",", userIds); // 每个userId已逗号分隔
        // 根据用户id查询用户，并转为UserVo集合 WHERE id IN (6, 2, 1) ORDER BY FIELD(id, 6, 2, 1)
        // FIELD函数用于根据指定的顺序对结果进行排序
        List<UserDTO> userDTOList = userService.query()
                .in("id", userIds).last("ORDER BY FIELD(id, " + userIdsStr + ")").list()
                .stream()
                .map(user -> BeanUtil.copyProperties(user, UserDTO.class))
                .collect(Collectors.toList());
        // 返回UserVo集合
        return Result.ok(userDTOList);

    }

    @Override
    public Result queryBlogOfFollow(Long lastId, Integer offset) {
        // lastId -- 作为上次末尾的时间戳,如果没有的话就是当前时间戳

        // 1.获取当前用户
        Long userId = UserHolder.getUser().getId();
        // 2.获取当前用户的收件箱
        // ZREVRANGEBYSCORE key max min [WITHSCORES] [limit offset count] --
        // 由分数降序查询,从max -- min符合范围,offset指的是从匹配的max开始后的第几个查询

        String key = FEED_KEY + userId;
        Set<TypedTuple<String>> reverseRangeByScoreWithScores = stringRedisTemplate.opsForZSet()
                .reverseRangeByScoreWithScores(key, 0, lastId, offset, 2);
        // 3.判断收件箱
        if (reverseRangeByScoreWithScores == null || CollUtil.isEmpty(reverseRangeByScoreWithScores)) {
            return Result.ok(Collections.emptyList());
        }

        // 4.解析数据,获取本次数据的offset和解析出blogId
        List<Long> blogIds = new ArrayList<>();
        long minTime = 0L;
        int os = 1; // 默认只有自己是相同的,偏移量是1,下次带着这个时间戳找到这个时间戳的下一个
        for (ZSetOperations.TypedTuple<String> typedTuple : reverseRangeByScoreWithScores) {
            // 循环遍历

            // 添加ids
            blogIds.add(Long.valueOf(typedTuple.getValue()));

            // 获取本次循环的时间戳,如果本次循环的时间戳和最小时间戳相同就是++
            long time = typedTuple.getScore().longValue();
            if (minTime == time) {
                // 相同就++
                os++;
            } else {
                // 不同就重置,说明这个相同的时间戳连续不会影响下次的os
                minTime = time;
                os = 1;
            }
        }

        // 查询数据库获取blog--这里还是因为数据库本身会使用IN关键字使用id排序导致不符合预期的时间结果,因此使用自定义的SQL
        String blogIdsStr = StrUtil.join(",", blogIds);
        List<Blog> blogs = query().in("id", blogIds).last("order by field(id, " + blogIdsStr + ")").list();
        for (Blog blog : blogs) {
            // 设置blog有关的用户
            queryBlogUser(blog);
            // 设置blog是否被点赞
            queryBlogIsLiked(blog);
        }

        // 封装滚动对象
        ScrollResult scrollResult = new ScrollResult();
        scrollResult.setList(blogs);
        scrollResult.setMinTime(minTime);
        scrollResult.setOffset(os);

        return Result.ok(scrollResult);
    }
}
