package com.hmdp.service.impl;

import static com.hmdp.utils.RedisConstants.LOCK_ORDER_KEY;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.connection.stream.Consumer;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.ReadOffset;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.connection.stream.StreamReadOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.Result;
import com.hmdp.entity.SeckillVoucher;
import com.hmdp.entity.VoucherOrder;
import com.hmdp.mapper.VoucherOrderMapper;
import com.hmdp.service.ISeckillVoucherService;
import com.hmdp.service.IVoucherOrderService;
import com.hmdp.utils.RedisIdWorker;
import com.hmdp.utils.UserHolder;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
@Slf4j
public class VoucherOrderServiceImpl extends ServiceImpl<VoucherOrderMapper, VoucherOrder>
        implements IVoucherOrderService {

    @Autowired
    private ISeckillVoucherService seckillVoucherService;

    @Autowired
    private RedisIdWorker redisIdWorker;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 释放锁脚本
     */
    private static final DefaultRedisScript<Long> SECKILL_SCRIPT;

    static {
        SECKILL_SCRIPT = new DefaultRedisScript<>();
        SECKILL_SCRIPT.setLocation(new ClassPathResource("seckill.lua"));
        SECKILL_SCRIPT.setResultType(Long.class);
    }

    /**
     * 异步读取消息队列持久化数据库线程池
     */
    public static final ExecutorService SECKILL_ORDER_EXECUTOR = Executors.newSingleThreadExecutor();

    /**
     * 当前类初始化完毕就立马执行该方法
     */
    @PostConstruct
    private void init() {
        // 执行线程任务
        SECKILL_ORDER_EXECUTOR.submit(new VoucherOrderHandler());
    }

    /**
     * 消息队列key
     */
    private final String QUEUE_NAME = "stream.orders";

    /**
     * VoucherOrderServiceImpl类的代理对象
     * 将代理对象的作用域进行提升，方面子线程取用
     */
    private IVoucherOrderService proxy;

    /**
     * 优惠券秒杀
     */
    @Transactional
    @Override
    public Result seckill(Long voucherId) {
        // 1.生成订单id,用户id
        Long orderId = redisIdWorker.nextId("order");
        Long userId = UserHolder.getUser().getId();
        // 2.使用lua脚本判断是否具有秒杀资格--有资格的添加到消息队列中,没有资格的直接返回没资格的信息
        Long res = stringRedisTemplate.execute(
                SECKILL_SCRIPT,
                Collections.emptyList(),
                userId.toString(),
                voucherId.toString(),
                orderId.toString());

        // 3.根据返回的信息来返回Result给前端
        if (res == 1L) {
            // 3.1没有库存
            log.info("返回结果给前端");
            return Result.fail("库存不足");
        }

        if (res == 2L) {
            // 3.2用户已经下过单
            log.info("返回结果给前端");
            return Result.fail("每个人只能下一单");
        }

        // 3.3 创建代理对象为后续调用
        // 索取锁成功，创建代理对象，使用代理对象调用第三方事务方法， 防止事务失效
        IVoucherOrderService proxy = (IVoucherOrderService) AopContext.currentProxy();
        this.proxy = proxy;
        // 3.4 用户秒杀成功,异步处理消息队列同时返回成功订单号
        log.info("返回结果给前端");
        return Result.ok(orderId);
    }

    /**
     * 优惠券订单处理器--匿名类用来为异步持久化线程使用
     */
    private class VoucherOrderHandler implements Runnable {

        @Override
        public void run() {
            while (true) {
                // 1.循环读取消息队列
                List<MapRecord<String, Object, Object>> messageList = stringRedisTemplate.opsForStream().read(
                        Consumer.from("g1", "c1"),
                        StreamReadOptions.empty().count(1).block(Duration.ofSeconds(1)),
                        StreamOffset.create(QUEUE_NAME, ReadOffset.lastConsumed()));

                // 2.判断消息队列情况
                if (messageList == null || messageList.isEmpty()) {
                    // 2.1消息队列没有消息,继续监听
                    continue;
                }

                // 2.2消息队列有消息
                log.info("消息队列存在消息");
                MapRecord<String, Object, Object> mapRecord = messageList.get(0); // 一次只消费消息,因此list中最多只有一条消息
                Map<Object, Object> messageMap = mapRecord.getValue(); // 获取值map
                VoucherOrder voucherOrder = BeanUtil.fillBeanWithMap(messageMap, new VoucherOrder(), false);

                // 3.处理消息
                try {
                    log.info("开始处理消息");
                    // 处理订单
                    handleVoucherOrder(voucherOrder);
                    // 确认信息
                    log.info("确认信息:{}", voucherOrder);
                    stringRedisTemplate.opsForStream().acknowledge(QUEUE_NAME, "g1", mapRecord.getId());
                } catch (Exception e) {
                    log.error("消息处理出现错误,转入处理异常");
                    // 处理异常产生的pending-list
                    handlePendingList();
                }

                // 4.继续监听
                continue;

            }

        }

    }

    /**
     * 处理订单
     * 
     * @param voucherOrder
     */
    public void handleVoucherOrder(VoucherOrder voucherOrder) {
        // 获取三个关键的信息
        Long userId = voucherOrder.getUserId();

        // 尝试获取锁
        RLock lock = redissonClient.getLock(LOCK_ORDER_KEY + userId);
        boolean isLock = lock.tryLock();
        if (!isLock) {
            // 获取锁失败,说明有线程已经获取该用户的锁id,直接报错就行
            log.error("一人只能下一单:{}", voucherOrder);
            return; // 返回后续不能继续执行了
        }

        // 获取锁成功
        try {
            log.info("获取锁成功", voucherOrder);
            // 使用代理对象调用,保证事务生效
            proxy.createVoucherOrder(voucherOrder);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }
    }

    // /**
    // * 优惠券秒杀
    // */
    // @Transactional
    // @Override
    // public Result seckill(Long voucherId) {
    // // 1.查询优惠券信息
    // SeckillVoucher seckillVoucher = seckillVoucherService.getById(voucherId);

    // // 2.判断秒杀是否开始
    // if (LocalDateTime.now().isBefore(seckillVoucher.getBeginTime())) {
    // // 2.1秒杀未开始,返回异常
    // log.info("秒杀未开始:{}", voucherId);
    // return Result.fail("秒杀未开始");
    // }
    // if (LocalDateTime.now().isAfter(seckillVoucher.getEndTime())) {
    // // 2.2秒杀已结束,返回异常信息
    // log.info("秒杀已结束:{}", voucherId);
    // return Result.fail("秒杀已结束");
    // }

    // // 2.3秒杀开始检查库存
    // Integer stock = seckillVoucher.getStock();
    // if (!(stock > 0)) {
    // // 3.1库存不足返回异常信息
    // log.info("库存不足:{}", voucherId);
    // return Result.fail("库存不足");
    // }

    // // 4.扣减库存并创建订单
    // Long userId = UserHolder.getUser().getId();

    // // // 使用悲观锁防止一人多单的超卖
    // // synchronized (userId.toString().intern()) {
    // // log.info("线程开始执行:{}", Thread.currentThread().getName());
    // // // 创建代理对象
    // // IVoucherOrderService proxy = (IVoucherOrderService)
    // // AopContext.currentProxy();
    // // // 调用代理对象的创建订单方法(防止事务失效)
    // // return proxy.createVoucherOrder(userId, voucherId);

    // // }

    // // // 使用分布式锁+lua脚本完成悲观锁解决一人多卖的问题
    // // SimpleRedisLock lock = new SimpleRedisLock(stringRedisTemplate, "order" +
    // // userId); // lock:order:{userId} =>
    // // // xxxthreadId
    // // 使用redisson的分布式锁替代自定义的简单分布式锁
    // RLock lock = redissonClient.getLock(LOCK_ORDER_KEY + userId);
    // boolean isLock = lock.tryLock();
    // if (!isLock) {
    // // 未获取锁,说明当前有其他机器线程持有锁,根据一人一单的限制,应该直接返回失败
    // log.info("当前用户:{}在此线程:{}无法获取锁:", userId, Thread.currentThread().getName());
    // return Result.fail("一个人只能下一单");
    // }

    // try {
    // log.info("获取锁成功:{}", Thread.currentThread().getName());
    // return createVoucherOrder(userId, voucherId);
    // } catch (Exception e) {
    // log.error("获取锁后执行业务失败:{}", Thread.currentThread().getName());
    // return Result.fail("服务器繁忙,请稍后再试");
    // } finally {
    // lock.unlock();
    // }
    // }

    /**
     * 处理异常时产生的pending-list
     */
    public void handlePendingList() {
        while (true) {
            // 1.循环读取pending-list队列
            List<MapRecord<String, Object, Object>> messageList = stringRedisTemplate.opsForStream().read(
                    Consumer.from("g1", "c1"),
                    StreamReadOptions.empty().count(1).block(Duration.ofSeconds(1)),
                    StreamOffset.create(QUEUE_NAME, ReadOffset.from("0"))); // 获取pending-list中的第一个

            // 2.判断pending-list队列情况
            if (messageList == null || messageList.isEmpty()) {
                // 2.1pending-list队列没有消息,继续监听
                break;
            }

            // 2.2消息队列有消息
            log.info("pending-list队列存在消息");
            MapRecord<String, Object, Object> mapRecord = messageList.get(0); // 一次只消费消息,因此list中最多只有一条消息
            Map<Object, Object> messageMap = mapRecord.getValue(); // 获取值map
            VoucherOrder voucherOrder = BeanUtil.fillBeanWithMap(messageMap, new VoucherOrder(), false);

            // 3.处理消息
            try {
                log.info("开始处理pending-list消息");
                // 处理订单
                handleVoucherOrder(voucherOrder);
                // 确认信息
                log.info("确认信息:{}", voucherOrder);
                stringRedisTemplate.opsForStream().acknowledge(QUEUE_NAME, "g1", mapRecord.getId());
            } catch (Exception e) {
                try {
                    log.error("pending-list消息处理出现错误,转入处理异常");
                    Thread.sleep(20);
                } catch (InterruptedException e1) {
                    // 出现休眠失败
                    e1.printStackTrace();
                }
                // 无需调用自己,本循环唯一方式就是pending-list是空的
            }

        }
    }

    @Override
    @Transactional
    public void createVoucherOrder(VoucherOrder voucherOrder) {
        // 1.获取用户订单数量
        Long userId = voucherOrder.getUserId();
        long count = this.count(new LambdaQueryWrapper<VoucherOrder>()
                .eq(VoucherOrder::getUserId, userId));

        if (count > 0) {
            // 2.1用户不是第一单
            log.error("用户不是第一单", voucherOrder);
        }

        // 2.2用户是第一单
        // 3.使用乐观锁更新库存

        // 3.2库存充足,更新库存
        Long voucherId = voucherOrder.getVoucherId();
        boolean isUpdate = seckillVoucherService.update(new LambdaUpdateWrapper<SeckillVoucher>()
                .eq(SeckillVoucher::getVoucherId, voucherId)
                .setSql("stock = stock - 1"));
        if (!isUpdate) {
            // 更新库存失败,返回异常信息
            log.error("更新库存失败", voucherOrder);
        }

        boolean isSave = save(voucherOrder);
        if (!isSave) {
            log.error("添加订单失败", voucherOrder);
        }

        log.info("此消息处理成功", voucherOrder);

    }

    /**
     * 用于同步秒杀订单
     */
    @Transactional
    @Override
    public Result createVoucherOrder(Long userId, Long voucherId) {
        // 1.获取用户订单数量
        long count = this.count(new LambdaQueryWrapper<VoucherOrder>()
                .eq(VoucherOrder::getUserId, userId));

        if (count > 0) {
            // 2.1用户不是第一单
            log.info("线程结束执行:{}", Thread.currentThread().getName());
            return Result.fail("一个人只能下一单");
        }

        // 2.2用户是第一单
        // 3.使用乐观锁更新库存

        // 3.2库存充足,更新库存
        boolean isUpdate = seckillVoucherService.update(new LambdaUpdateWrapper<SeckillVoucher>()
                .eq(SeckillVoucher::getVoucherId, voucherId)
                .setSql("stock = stock - 1"));
        if (!isUpdate) {
            // 更新库存失败,返回异常信息
            log.info("线程结束执行:{}", Thread.currentThread().getName());
            return Result.fail("服务器繁忙");
        }

        // 3.3创建订单,返回订单id
        Long orderId = redisIdWorker.nextId("order");
        VoucherOrder voucherOrder = new VoucherOrder();
        voucherOrder.setId(orderId); // 订单id

        voucherOrder.setUserId(userId); // 用户id
        voucherOrder.setVoucherId(voucherId); // 秒杀优惠券id
        boolean isSave = save(voucherOrder);
        if (!isSave) {
            log.info("线程结束执行:{}", Thread.currentThread().getName());
            return Result.fail("订单创建失败");
        }

        log.info("线程结束执行:{}", Thread.currentThread().getName());
        return Result.ok(orderId);
    }

}
