package com.hmdp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmdp.dto.LoginFormDTO;
import com.hmdp.dto.Result;
import com.hmdp.entity.User;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
public interface IUserService extends IService<User> {

    /**
     * 发送验证码
     * 
     * @param phone
     * @return
     */
    Result sendCode(String phone);

    /**
     * 用户登陆
     * 
     * @param loginForm
     * @return
     */
    Result login(LoginFormDTO loginForm);

    /**
     * 用户签到
     * 
     * @return
     */
    Result sign();

    /**
     * 签到统计
     * 
     * @return
     */
    Result signCount();

}
