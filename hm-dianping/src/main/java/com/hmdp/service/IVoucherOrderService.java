package com.hmdp.service;

import com.hmdp.dto.Result;
import com.hmdp.entity.VoucherOrder;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
public interface IVoucherOrderService extends IService<VoucherOrder> {

    /**
     * 完成优惠券的秒杀
     * 
     * @param voucherId
     * @return
     */
    Result seckill(Long voucherId);

    Result createVoucherOrder(Long userId, Long voucherId);

    void createVoucherOrder(VoucherOrder voucherOrder);

}
