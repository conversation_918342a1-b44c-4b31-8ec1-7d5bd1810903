package com.hmdp.service;

import com.hmdp.dto.Result;
import com.hmdp.entity.Blog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
public interface IBlogService extends IService<Blog> {

    Result saveBlog(Blog blog);

    /**
     * 根据id获取blog
     * 
     * @param id
     * @return
     */
    Result queryBlogById(Long id);

    /**
     * 用户点赞blog
     * 
     * @param id
     * @return
     */
    Result likeBlog(Long id);

    /**
     * 查询点赞前五名
     * 
     * @param id
     * @return
     */
    Result queryBlogLikes(Long id);

    /**
     * 获取关注用户的笔记流
     * 
     * @param lastId
     * @param offset
     * @return
     */
    Result queryBlogOfFollow(Long lastId, Integer offset);
}
