package com.hmdp.service;

import com.hmdp.dto.Result;
import com.hmdp.entity.Shop;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
public interface IShopService extends IService<Shop> {

    /**
     * 通过id获取店铺信息
     * 
     * @param id
     * @return
     */
    Result queryShopById(Long id);

    /**
     * 更新店铺
     * 
     * @param shop
     * @return
     */
    Result updateShop(Shop shop);

    /**
     * 根据类型获取店铺
     * 
     * @param typeId
     * @param current
     * @param x
     * @param y
     * @return
     */
    Result queryShopByType(Integer typeId, Integer current, Double x, Double y);

}
