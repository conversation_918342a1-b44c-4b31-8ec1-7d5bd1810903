package com.hmdp.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private String port;

    @Bean
    public RedissonClient redissonClient() {
        // 获取redisson配置对象
        Config config = new Config();
        // 添加redis的连接url地址,这里添加单节点,也可以添加集群的模式
        config.useSingleServer().setAddress("redis://" + this.host + ":" + this.port);
        // 获取RedissonClient返回注入IOC容器中
        return Redisson.create(config);
    }
}
