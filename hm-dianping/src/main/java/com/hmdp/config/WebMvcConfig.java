package com.hmdp.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.hmdp.interceptor.LoginInterceptor;
import com.hmdp.interceptor.TokenRefreshInterceptor;

/**
 * 用于添加拦截器
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Autowired
    private LoginInterceptor loginInterceptor;

    @Autowired
    private TokenRefreshInterceptor tokenRefreshInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 1.添加token刷新拦截器--order为0
        registry.addInterceptor(tokenRefreshInterceptor)
                .addPathPatterns("/**")
                .order(0);

        // 2.添加登录拦截器--order为1
        registry.addInterceptor(loginInterceptor)
                .excludePathPatterns(
                        "/user/code",
                        "/user/login",
                        "/blog/hot",
                        "/shop/**",
                        "/shop-type/**",
                        "/upload/**",
                        "/voucher/**")
                .order(1); // 优先级默认都是0，值越大优先级越低
    }
}
