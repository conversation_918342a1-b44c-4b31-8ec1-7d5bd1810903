package com.hmdp.utils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 基于redis的id生成器
 */
@Component
public class RedisIdWorker {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final long BEGIN_TIMESTAMP = 1735689600; // 2025年01月01日00时00分00秒

    private final long TIMESTAMP_SHIFT_BITS = 32;

    private final String ID_PREFIX = "id:";

    // 生成的id遵循下列的格式
    // [63]|[62:32]|[31:0] -- 符号位|时间戳|redis自增计数
    // 符号位0 -- 非负数, 1 -- 负数
    // 时间戳基于当前时间戳 - 基准时间戳
    // redis自增计数 -- 获取的是多少就是多少

    public Long nextId(String keyPrefix) {
        // 1.生成时间戳
        LocalDateTime now = LocalDateTime.now();
        long epochSecond = now.toEpochSecond(ZoneOffset.UTC);
        long timestamp = epochSecond - BEGIN_TIMESTAMP;

        // 2. 使用当天yyyy:MM:dd作为key获取redis计数
        // 2.1 获取key
        String date = now.format(DateTimeFormatter.ofPattern("yyyy:MM:dd"));
        // 2.2 获取redis计数
        Long count = stringRedisTemplate.opsForValue().increment(ID_PREFIX + keyPrefix + ":" + date);

        // 3.拼接时间戳和redis计数--使用移位和或操作拼接提高速度
        return timestamp << TIMESTAMP_SHIFT_BITS | count;
    }

    public static void main(String[] args) {
        //
    }
}
