package com.hmdp.utils;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component; // 工具类通常用 @Component 或 @Service

import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.hmdp.utils.RedisConstants.CACHE_NULL_TTL;
// 假设 RedisConstants 中定义了 LOCK_TTL 和 CACHE_TTL
// public static final Long LOCK_TTL = 10L; // 锁的过期时间，单位秒
// public static final Long CACHE_COMMON_TTL = 30L; // 普通缓存的过期时间，单位分钟
import static com.hmdp.utils.RedisConstants.LOCK_SHOP_TTL;

@Slf4j
@Component // 工具类通常用 @Component
public class CacheClient {

    private final StringRedisTemplate stringRedisTemplate;

    // 推荐将线程池定义为 Spring Bean，由 Spring 管理生命周期
    // 这里作为示例，如果不在 Spring 配置中管理，需要注意资源关闭
    private static final ExecutorService CACHE_REBUILD_EXECUTOR = Executors.newFixedThreadPool(10);

    // 构造函数注入 StringRedisTemplate
    public CacheClient(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 将数据序列化并缓存到Redis，带过期时间。
     * 支持直接缓存String类型，或将T类型序列化为JSON字符串。
     *
     * @param key      缓存key
     * @param value    要缓存的数据
     * @param timeout  过期时间
     * @param timeUnit 过期时间单位
     * @param <T>      数据类型
     */
    public <T> void set(String key, T value, Long timeout, TimeUnit timeUnit) {
        if (value instanceof String) {
            // 如果值已经是String类型，直接存储
            stringRedisTemplate.opsForValue().set(key, (String) value, timeout, timeUnit);
        } else {
            // 否则，序列化为JSON字符串再存储
            stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(value), timeout, timeUnit);
        }
    }

    /**
     * 将数据封装为RedisData（带逻辑过期时间）并写入缓存。
     *
     * @param key      缓存key
     * @param data     要缓存的真实数据
     * @param timeout  缓存时长
     * @param timeUnit 缓存时长单位
     * @param <T>      数据类型
     */
    public <T> void setWithLogicalExpireTime(String key, T data, Long timeout, TimeUnit timeUnit) {
        RedisData redisData = new RedisData();
        redisData.setData(data);
        // 将传入的timeout转换为秒，并加上当前时间，作为逻辑过期时间
        long seconds = timeUnit.toSeconds(timeout);
        redisData.setExpireTime(LocalDateTime.now().plusSeconds(seconds));

        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(redisData));
        log.debug("数据 {} 已缓存，逻辑过期时间：{}", key, redisData.getExpireTime());
    }

    /**
     * 通用方法：解决缓存穿透（缓存空对象）
     *
     * @param keyPrefix  缓存key的前缀
     * @param id         业务ID
     * @param type       返回数据的类型
     * @param dbFallback 数据库查询函数，接受ID，返回T
     * @param timeout    缓存过期时间
     * @param timeUnit   缓存过期时间单位
     * @param <T>        数据类型
     * @param <ID>       ID类型
     * @return 业务数据T，如果不存在则返回null
     */
    public <T, ID> T queryWithPassThrough(
            String keyPrefix,
            ID id,
            Class<T> type,
            Function<ID, T> dbFallback,
            Long timeout,
            TimeUnit timeUnit) {

        String cacheKey = keyPrefix + id;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);

        // 1. 判断缓存是否命中
        if (StrUtil.isNotBlank(json)) {
            // 命中有效数据
            return JSONUtil.toBean(json, type);
        }
        // 判断命中的是否是空值（""）
        if (json != null) { // 如果json不为null，且上面isNotBlank为false，则说明是空字符串
            log.debug("缓存命中空值：{}", cacheKey);
            return null; // 返回null，表示数据不存在
        }

        // 2. 缓存未命中，查询数据库
        log.debug("缓存未命中，查询数据库：{}", cacheKey);
        T data = dbFallback.apply(id);

        // 3. 数据库数据判断并写入缓存
        if (data == null) {
            // 数据库不存在，缓存空值，设置短TTL
            set(cacheKey, "", CACHE_NULL_TTL, TimeUnit.MINUTES);
            log.warn("数据库未找到数据 {}，缓存空值。", cacheKey);
            return null;
        } else {
            // 数据库存在，缓存真实数据
            set(cacheKey, data, timeout, timeUnit);
            log.debug("数据库找到数据 {}，并缓存。", cacheKey);
            return data;
        }
    }

    /**
     * 通用方法：使用逻辑过期解决缓存击穿，同时处理缓存穿透。
     * 针对首次加载或缓存完全失效的情况，会调用内部的互斥锁方式进行加载。
     *
     * @param keyPrefix     缓存key的前缀
     * @param lockKeyPrefix 锁key的前缀
     * @param id            业务ID
     * @param type          返回数据的类型
     * @param dbFallback    数据库查询函数，接受ID，返回T
     * @param timeout       缓存过期时间（用于逻辑过期时间计算）
     * @param timeUnit      缓存过期时间单位
     * @param <T>           数据类型
     * @param <ID>          ID类型
     * @return 业务数据T，如果不存在则返回null
     */
    public <T, ID> T queryWithLogicalExpireTime(
            String keyPrefix,
            String lockKeyPrefix,
            ID id,
            Class<T> type,
            Function<ID, T> dbFallback,
            Long timeout, // 此处的 timeout 将用于计算逻辑过期时间
            TimeUnit timeUnit) {

        String cacheKey = keyPrefix + id;
        String redisDataJson = stringRedisTemplate.opsForValue().get(cacheKey);

        // 1. 判断缓存是否命中（这里处理了完全未命中和命中空值两种穿透情况）
        if (StrUtil.isBlank(redisDataJson)) {
            // 缓存未命中（null）或命中空值（""）
            // 对于逻辑过期，理论上所有热点数据都应该预热。
            // 如果这里为空，说明是冷数据首次访问，或者缓存被手动删除/过期。
            // 此时应该走互斥锁方式，去数据库查询并写入（真实数据或空值），避免穿透和首次加载击穿
            log.info("缓存 {} 未命中或为空，转入首次加载处理。", cacheKey);
            return queryWithMutexForFirstLoad(keyPrefix, lockKeyPrefix, id, type, dbFallback, timeout, timeUnit);
        }

        // 2. 缓存命中，反序列化为RedisData对象
        RedisData redisData = JSONUtil.toBean(redisDataJson, RedisData.class);
        T data = JSONUtil.toBean((JSONObject) redisData.getData(), type); // Hutool JSONUtil.toBean 转换 JSONObject
                                                                          // 到具体的T对象
        LocalDateTime expireTime = redisData.getExpireTime();

        // 3. 判断是否逻辑过期
        if (expireTime.isAfter(LocalDateTime.now())) {
            // 3.1 未过期，直接返回数据
            log.debug("缓存 {} 未逻辑过期，直接返回。", cacheKey);
            return data;
        }

        // 3.2 已逻辑过期，尝试获取重建锁
        log.info("缓存 {} 已逻辑过期，尝试获取锁进行异步重建。", cacheKey);
        String lockKey = lockKeyPrefix + id;
        boolean isLock = tryLock(lockKey);

        // 4. 判断是否获取锁成功
        if (isLock) {
            // 4.1 获取锁成功，开启独立线程异步重建缓存
            log.info("成功获取锁 {}，启动异步重建任务。", lockKey);
            CACHE_REBUILD_EXECUTOR.submit(() -> {
                try {
                    // 异步任务中的二次检查：防止在获取锁到执行异步任务之间，另一个线程已经重建完成
                    String innerRedisDataJson = stringRedisTemplate.opsForValue().get(cacheKey);
                    if (StrUtil.isNotBlank(innerRedisDataJson)) {
                        RedisData innerRedisData = JSONUtil.toBean(innerRedisDataJson, RedisData.class);
                        if (innerRedisData.getExpireTime().isAfter(LocalDateTime.now())) {
                            log.debug("异步任务中发现缓存已由其他线程重建，无需重复操作。Key: {}", cacheKey);
                            return; // 另一个线程已经重建完成，当前异步任务无需重复
                        }
                    }

                    // 真正执行数据库查询并更新缓存
                    T newData = dbFallback.apply(id);
                    if (newData != null) {
                        setWithLogicalExpireTime(cacheKey, newData, timeout, timeUnit); // 缓存真实数据带逻辑过期
                    } else {
                        // 如果数据库返回null，表示数据不存在，也缓存空值，但TTL短
                        set(cacheKey, "", CACHE_NULL_TTL, TimeUnit.MINUTES); // 缓存空值
                        log.warn("异步重建任务：数据库未找到数据 {}，缓存空值。", cacheKey);
                    }
                } catch (Exception e) {
                    log.error("异步重建缓存失败，Key: {}", cacheKey, e);
                } finally {
                    unLock(lockKey); // 确保锁在重建完成后释放
                    log.info("异步重建任务完成，释放锁 {}.", lockKey);
                }
            });
        } else {
            // 4.2 获取锁失败，说明已有其他线程在重建，无需重复操作
            log.info("获取锁 {} 失败，其他线程正在重建，返回当前旧数据。", lockKey);
        }

        // 5. 无论是否获取锁成功，都立即返回当前缓存中的旧数据
        return data; // 返回旧数据
    }

    /**
     * 辅助方法：处理缓存未命中（包括穿透）时的首次加载，使用互斥锁。
     * 适用于冷数据首次访问或缓存被删除的情况，确保数据从DB加载时不会击穿。
     *
     * @param keyPrefix     缓存key的前缀
     * @param lockKeyPrefix 锁key的前缀
     * @param id            业务ID
     * @param type          返回数据的类型
     * @param dbFallback    数据库查询函数，接受ID，返回T
     * @param timeout       缓存过期时间（用于逻辑过期时间计算）
     * @param timeUnit      缓存过期时间单位
     * @param <T>           数据类型
     * @param <ID>          ID类型
     * @return 业务数据T，如果不存在则返回null
     */
    public <T, ID> T queryWithMutexForFirstLoad(
            String keyPrefix,
            String lockKeyPrefix,
            ID id,
            Class<T> type,
            Function<ID, T> dbFallback,
            Long timeout,
            TimeUnit timeUnit) {

        String cacheKey = keyPrefix + id;
        String lockKey = lockKeyPrefix + id;
        T data = null;
        boolean isLock = false; // 标记是否成功获取锁

        try {
            // 1. 尝试获取锁
            isLock = tryLock(lockKey);

            // 2. 判断是否获取锁成功
            if (!isLock) {
                // 获取锁失败，休眠并重试（避免CPU空转，同时给其他线程机会）
                Thread.sleep(50);
                // 递归调用，确保最终能获取到数据或明确的null。
                // 生产环境建议使用循环+重试次数限制，避免栈溢出。
                log.warn("首次加载获取锁 {} 失败，休眠重试。", lockKey);
                return queryWithMutexForFirstLoad(keyPrefix, lockKeyPrefix, id, type, dbFallback, timeout, timeUnit);
            }

            // 3. 获取锁成功，进行“二次检查”：再次确认缓存是否已被填充
            String redisDataJson = stringRedisTemplate.opsForValue().get(cacheKey);
            if (StrUtil.isNotBlank(redisDataJson)) {
                // 二次检查命中有效数据 (可能是逻辑过期数据，也可能是新数据)
                // 在首次加载场景，如果这里命中，说明其他线程已经处理了
                RedisData redisData = JSONUtil.toBean(redisDataJson, RedisData.class);
                log.debug("首次加载获取锁后二次检查，缓存已命中。Key: {}", cacheKey);
                // 返回真实数据（即使是逻辑过期数据，因为首次加载时也需要返回一个T）
                return JSONUtil.toBean((JSONObject) redisData.getData(), type);
            }
            if (redisDataJson != null) { // 二次检查命中空值（""）
                log.debug("首次加载获取锁后二次检查，缓存命中空值。Key: {}", cacheKey);
                return null;
            }

            // 4. 缓存中确实没有，查询数据库
            log.info("首次加载获取锁后，缓存仍为空，查询数据库：{}", cacheKey);
            data = dbFallback.apply(id);

            // 5. 数据库数据判断并缓存
            if (data == null) {
                // 数据库信息不存在，缓存空值，短TTL
                set(cacheKey, "", CACHE_NULL_TTL, TimeUnit.MINUTES);
                log.warn("首次加载：数据库未找到数据 {}，缓存空值。", cacheKey);
            } else {
                // 数据库信息存在，缓存真实数据，封装为RedisData，设置逻辑过期时间
                setWithLogicalExpireTime(cacheKey, data, timeout, timeUnit); // 使用逻辑过期方式缓存
                log.info("首次加载：数据库找到数据 {}，并缓存为逻辑过期数据。", cacheKey);
            }
            return data;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("缓存首次加载被中断，Key: {}", cacheKey, e);
            // 抛出运行时异常，让调用方处理
            throw new RuntimeException("系统繁忙，操作被中断", e);
        } catch (Exception e) {
            log.error("缓存首次加载失败，Key: {}", cacheKey, e);
            // 抛出运行时异常
            throw new RuntimeException("系统繁忙，请稍后再试", e);
        } finally {
            if (isLock) {
                unLock(lockKey);
            }
        }
    }

    /**
     * 释放分布式锁
     * **重要：生产环境必须使用 Lua 脚本实现原子性的判断和删除，以防止误删其他线程的锁。**
     *
     * @param lockKey 锁的键
     */
    private void unLock(String lockKey) {
        // 生产级安全的解锁，防止误删，需要存储线程ID或UUID并使用Lua脚本
        // 这里仅为示例，实际应使用 Redisson 或自定义 Lua 脚本
        stringRedisTemplate.delete(lockKey);
        log.debug("锁 {} 已释放。", lockKey);
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁的键
     * @return 是否成功获取锁
     */
    private boolean tryLock(String lockKey) {
        // 存储线程ID，用于安全释放锁
        Boolean isLock = stringRedisTemplate.opsForValue().setIfAbsent(
                lockKey,
                String.valueOf(Thread.currentThread().getId()), // 存储线程ID
                LOCK_SHOP_TTL, // 值是10L
                TimeUnit.SECONDS // **这里改为 SECONDS**
        );
        log.debug("尝试获取锁 {}，结果：{}", lockKey, isLock);
        return BooleanUtil.isTrue(isLock); // 避免NPE
    }
}
