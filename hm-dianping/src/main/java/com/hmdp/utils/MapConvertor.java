package com.hmdp.utils;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil; // 引入Hutool的JSON工具

@Component
public class MapConvertor {

    /**
     * 将Java对象转换为Map<String, String>类型。
     * 对于复杂对象（如嵌套对象、集合）或特殊类型（如Date），
     * 会将其转换为JSON字符串。
     *
     * @param obj 要转换的Java对象
     * @return 转换后的Map<String, String>，如果输入对象为null则返回null
     */
    public static Map<String, String> convert(Object obj) {
        if (obj == null) {
            return null;
        }

        // 1. 使用Hutool工具将Java对象转换为Map<String, Object>类型
        // BeanUtil.beanToMap 会将对象的属性名作为key，属性值作为value。
        // 对于嵌套对象或集合，其value仍然是对应的对象或集合实例。
        Map<String, Object> tempMap = BeanUtil.beanToMap(obj);

        // 2. 创建一个HashMap来存储最终的Map<String, String>结果
        Map<String, String> resultMap = new HashMap<>();

        // 3. 遍历临时的Map<String, Object>，将所有Object类型的值转换为String类型
        for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
            String key = entry.getKey(); // 获取当前键
            Object value = entry.getValue(); // 获取当前值（可能是各种类型）
            String stringValue; // 用于存储转换后的字符串值

            // 根据值的类型进行不同的转换处理
            if (value == null) {
                // 如果值为 null，统一转换为一个空字符串。
                // 你也可以选择转换为 "null" 字符串，或者直接跳过不放入map，根据业务需求决定。
                stringValue = null;
            } else if (value instanceof String) {
                // 如果值本身就是String类型，直接使用它，避免JSONUtil可能添加的额外引号
                stringValue = (String) value;
            } else {
                // 对于所有其他类型（包括基本类型包装类、Date、List、嵌套对象等），
                // 统一使用 Hutool 的 JSONUtil.toJsonStr() 将其转换为JSON字符串。
                // JSONUtil.toJsonStr() 对于基本类型会直接转为字符串（如 123 -> "123"），
                // 对于日期会转为格式化的日期字符串，对于集合和嵌套对象会转为JSON格式的字符串。
                try {
                    stringValue = String.valueOf(value);
                } catch (Exception e) {
                    // 如果JSON转换失败（极少发生，除非对象结构不支持JSON序列化），
                    // 则退回到使用Object的toString()方法。
                    System.err.println("警告：将键 '" + key + "' 的值转换为JSON字符串时出错。使用默认toString()。错误信息: " + e.getMessage());
                    stringValue = String.valueOf(value);
                }
            }

            // 将转换后的键值对存入最终结果Map
            resultMap.put(key, stringValue);
        }

        return resultMap;
    }
}
