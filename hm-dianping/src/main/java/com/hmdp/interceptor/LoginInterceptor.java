package com.hmdp.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.hmdp.dto.UserDTO;
import com.hmdp.utils.UserHolder;

import cn.hutool.http.HttpStatus;

/**
 * 登录拦截器,用于拦截未登录用户的请求
 */
@Component
public class LoginInterceptor implements HandlerInterceptor {

    /**
     * 在请求处理之前进行拦截
     * 
     * 
     * @param request
     * @param response
     * @param handler
     * @return
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        // 1.从ThreadLocal中获取用户信息
        UserDTO userDTO = UserHolder.getUser();

        if (userDTO == null) {
            // 2.1用户不存在不放行
            response.setStatus(HttpStatus.HTTP_NOT_AUTHORITATIVE); // 设置401状态码
            return false;
        }

        // 2.2用户存在放行
        return true;
    }

}
