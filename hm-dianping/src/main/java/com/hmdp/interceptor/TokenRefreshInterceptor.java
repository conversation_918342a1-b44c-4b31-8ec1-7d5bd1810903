package com.hmdp.interceptor;

import static com.hmdp.utils.RedisConstants.LOGIN_USER_KEY;
import static com.hmdp.utils.RedisConstants.LOGIN_USER_TTL;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.hmdp.dto.UserDTO;
import com.hmdp.utils.UserHolder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 刷新用户token过期时间
 * 将用户信息放入ThreadLocal
 */
@Component
public class TokenRefreshInterceptor implements HandlerInterceptor {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 前置刷新和信息放置
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        // 1.获取用户token
        String token = request.getHeader("authorization");
        if (StrUtil.isBlank(token)) {
            // token为空无需刷新和放置直接放行
            return true;
        }

        // 2.判断用户是否存在
        String tokenKey = LOGIN_USER_KEY + token;
        Map<Object, Object> userMap = stringRedisTemplate.opsForHash().entries(tokenKey);
        if (userMap.isEmpty()) {
            // 用户不存在无需刷新直接放行
            return true;
        }

        // 3.放置信息
        UserDTO userDTO = BeanUtil.mapToBean(userMap, UserDTO.class, false, null);
        UserHolder.saveUser(userDTO);

        // 4.刷新token过期时间
        stringRedisTemplate.expire(tokenKey, LOGIN_USER_TTL, TimeUnit.SECONDS);

        // 4.放行
        return true;
    }

    /**
     * 后置删除信息
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            @Nullable Exception ex) throws Exception {
        // 1.删除ThreadLocal中的用户信息
        UserHolder.removeUser();
    }
}
