-- 1.1用户id
local userId = ARGV[1]
-- 1.2优惠券id
local voucherId = ARGV[2]
-- 1.3订单id
local orderId = ARGV[3]

-- 2.1 库存key--用来储存优惠券库存
local stockKey = "seckill:stock:" .. voucherId
-- 2.2 订单key--用来储存用户,防止用户超买
local orderKey = "seckill:order" .. voucherId

-- 判断是否有库存
if(tonumber(redis.call("GET",stockKey)) <= 0) then
    -- 3.1库存不足
    return 1;
end

-- 3.2库存充足,判断用户有没有下过单
if(redis.call("SISMEMBER",orderKey,userId) == 1 ) then
    -- 4.用户已经下单,返回2
    return 2
end

-- 5.用户没有下单,需要执行下单相关业务
-- 5.1扣减库存
redis.call("INCRBY",stockKey,-1)
-- 5.2将用户放入下单集合
redis.call("SADD",orderKey,userId)
-- 5.3添加消息队列
redis.call("XADD","stream.orders","*","userId",userId,"voucherId",voucherId,"id",orderId)
-- 5.4返回0
return 0

