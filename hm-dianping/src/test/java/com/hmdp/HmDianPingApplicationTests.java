package com.hmdp;

import static com.hmdp.utils.RedisConstants.CACHE_SHOP_KEY;
import static com.hmdp.utils.RedisConstants.CACHE_SHOP_TTL;
import static com.hmdp.utils.RedisConstants.SHOP_GEO_KEY;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.StringRedisTemplate;

import com.hmdp.entity.Shop;
import com.hmdp.service.IShopService;
import com.hmdp.utils.RedisData;
import com.hmdp.utils.RedisIdWorker;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@SpringBootTest
@Slf4j
class HmDianPingApplicationTests {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IShopService shopService;

    @Autowired
    private RedisIdWorker redisIdWorker;

    @Autowired
    private RedissonClient redissonClient;

    private ExecutorService es = Executors.newFixedThreadPool(500);

    // @Test
    public void queryShopById() {
        Long id = 1L;
        Shop shop = shopService.getById(id);
        RedisData redisData = new RedisData();
        redisData.setData(shop);
        redisData.setExpireTime(LocalDateTime.now().plusSeconds(CACHE_SHOP_TTL));

        // 2.添加缓存
        String key = CACHE_SHOP_KEY + id;
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(redisData));
    }

    // @Test
    public void testRedisIdWorker() {
        System.out.println(redisIdWorker.nextId("order"));
        System.out.println(redisIdWorker.nextId("order"));
        System.out.println(redisIdWorker.nextId("order"));
        System.out.println(redisIdWorker.nextId("order"));
        System.out.println(redisIdWorker.nextId("order"));
    }

    /**
     * 测试分布式ID生成器的性能，以及可用性
     */
    // @Test
    public void testNextId() throws InterruptedException {
        // 使用CountDownLatch让线程同步等待
        CountDownLatch latch = new CountDownLatch(300);
        // 创建线程任务
        Runnable task = () -> {
            for (int i = 0; i < 100; i++) {
                long id = redisIdWorker.nextId("order");
                System.out.println("id = " + id);
            }
            // 等待次数-1
            latch.countDown();
        };
        long begin = System.currentTimeMillis();
        // 创建300个线程，每个线程创建100个id，总计生成3w个id
        for (int i = 0; i < 300; i++) {
            es.submit(task);
        }
        // 线程阻塞，直到计数器归0时才全部唤醒所有线程
        latch.await();
        long end = System.currentTimeMillis();
        System.out.println("生成3w个id共耗时" + (end - begin) + "ms");
    }

    private Lock lock;

    /**
     * 方法1获取一次锁
     */
    // @Test
    void method1() {
        boolean isLock = false;
        // 创建锁对象
        lock = redissonClient.getLock("lock");
        try {
            isLock = lock.tryLock();
            if (!isLock) {
                log.error("获取锁失败，1");
                return;
            }
            log.info("获取锁成功，1");
            method2();
        } finally {
            if (isLock) {
                log.info("释放锁，1");
                lock.unlock();
            }
        }
    }

    /**
     * 方法二再获取一次锁
     */
    void method2() {
        boolean isLock = false;
        try {
            isLock = lock.tryLock();
            if (!isLock) {
                log.error("获取锁失败, 2");
                return;
            }
            log.info("获取锁成功，2");
        } finally {
            if (isLock) {
                log.info("释放锁，2");
                lock.unlock();
            }
        }
    }

    // @Test
    public void loadShopData() {
        // 查询店铺信息
        List<Shop> list = shopService.list();
        // 根据店铺类型分类
        Map<Long, List<Shop>> map = list.stream().collect(Collectors.groupingBy(Shop::getTypeId));
        // 根据类型放入 shop:geo:type ==> shop中
        for (Entry<Long, List<Shop>> entry : map.entrySet()) {
            // 获取key
            String key = SHOP_GEO_KEY + entry.getKey();

            // 获取list
            List<Shop> shops = entry.getValue();

            // 构造坐标list
            List<RedisGeoCommands.GeoLocation<String>> locations = new ArrayList<>();
            for (Shop shop : shops) {
                locations.add(
                        new RedisGeoCommands.GeoLocation<String>(
                                shop.getId().toString(),
                                new Point(shop.getX(), shop.getY())));

            }

            stringRedisTemplate.opsForGeo().add(key, locations);
        }
    }

    @Test
    public void testHyperLoglog() {
        // 构造假的userId--1000_000
        int j = 0;
        String[] values = new String[1000];
        for (int i = 0; i < 100000000; i++) {
            j = i % 1000; // 1000发送一次
            values[j] = "user_" + i;
            if (j == 999) {
                stringRedisTemplate.opsForHyperLogLog().add("hl2", values);
            }
        }

        // 统计数量
        Long size = stringRedisTemplate.opsForHyperLogLog().size("hl2");
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
        System.out.println(size);
    }
}
